:root {
    --primary-color: #1a73e8;
    --sidebar-bg: #f0f4f9;
    --chat-bg: #ffffff;
    --text-color: #3c4043;
    --subtle-text-color: #5f6368;
    --user-msg-bg: #1a73e8;
    --ai-msg-bg: #f1f3f4;
    --border-color: #e0e2e6;
    --box-shadow: 0 1px 3px 0 rgba(0,0,0,.1), 0 1px 2px 0 rgba(0,0,0,.06);
}

body {
    font-family: 'Noto Sans SC', 'Google Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    margin: 0;
    background-color: #eaf0f6;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    color: var(--text-color);
}

#app-container, #api-key-view {
    width: 100%;
    max-width: 1024px;
    height: 95vh;
    max-height: 900px;
    background-color: var(--chat-bg);
    border-radius: 16px;
    box-shadow: var(--box-shadow);
    display: flex;
    overflow: hidden;
}

#api-key-view {
    padding: 40px; text-align: center; flex-direction: column; justify-content: center;
}
#api-key-view h2 { color: var(--primary-color); margin-bottom: 20px; font-weight: 500; }
#api-key-view p { font-size: 14px; color: var(--subtle-text-color); margin-bottom: 25px; }
#api-key-input { width: 100%; padding: 12px; border: 1px solid var(--border-color); border-radius: 12px; font-size: 16px; margin-bottom: 20px; box-sizing: border-box; }
#submit-api-key-button { background-color: var(--primary-color); color: white; padding: 12px 25px; border: none; border-radius: 12px; font-size: 16px; font-weight: 500; cursor: pointer; transition: background-color 0.3s; }

/* --- Sidebar --- */
#sidebar {
    width: 260px; background-color: var(--sidebar-bg); border-right: 1px solid var(--border-color);
    display: flex; flex-direction: column; flex-shrink: 0; padding: 12px; box-sizing: border-box;
}
#new-chat-button {
    display: flex; align-items: center; justify-content: center; gap: 8px;
    width: 100%; padding: 12px; background-color: #fff; color: var(--primary-color);
    border: 1px solid var(--primary-color); border-radius: 8px; font-size: 15px; font-weight: 500;
    cursor: pointer; margin-bottom: 15px; transition: all 0.2s ease;
}
#new-chat-button:hover { background-color: var(--ai-msg-bg); }
#new-chat-button svg { width: 18px; height: 18px; }

#chat-list { list-style: none; padding: 0; margin: 0; overflow-y: auto; flex-grow: 1; }
#chat-list::-webkit-scrollbar { width: 4px; }
#chat-list::-webkit-scrollbar-thumb { background: #ccc; border-radius: 2px; }

#chat-list li {
    padding: 12px; border-radius: 8px; cursor: pointer; white-space: nowrap;
    overflow: hidden; text-overflow: ellipsis; font-size: 14px; margin-bottom: 5px;
    transition: background-color 0.2s ease;
}
#chat-list li:hover { background-color: #e8eaf6; }
#chat-list li.active-chat { background-color: #e8eaf6; color: var(--primary-color); font-weight: 500; }

/* --- Main Chat Area --- */
#chat-view-container { display: none; flex-direction: column; height: 100%; flex-grow: 1; }
header {
    background-color: var(--chat-bg); color: var(--text-color); padding: 10px 20px; text-align: center;
    font-size: 16px; font-weight: 500; flex-shrink: 0; border-bottom: 1px solid var(--border-color);
    display: flex; justify-content: space-between; align-items: center;
}
#chat-title { white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
#delete-chat-button {
    background: none; border: none; font-size: 20px; cursor: pointer; color: var(--subtle-text-color);
    padding: 5px; border-radius: 50%; display: flex; align-items: center; justify-content: center;
    transition: background-color 0.2s ease;
}
#delete-chat-button:hover { background-color: #f1f3f4; }
#delete-chat-button svg { width: 20px; height: 20px; }

#chatbox { flex-grow: 1; padding: 20px; overflow-y: auto; display: flex; flex-direction: column; }

/* Welcome Screen */
#welcome-screen {
    display: none; flex-direction: column; align-items: center; justify-content: center; height: 100%; text-align: center;
}
#welcome-screen .welcome-logo {
    width: 64px; height: 64px; background-color: var(--primary-color); color: white;
    border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 20px;
}
#welcome-screen .welcome-logo svg { width: 36px; height: 36px; }
#welcome-screen h2 { margin: 0 0 10px 0; font-weight: 500; }
#welcome-screen p { color: var(--subtle-text-color); margin-top: 0; }
.prompt-examples { display: flex; gap: 10px; margin-top: 20px; flex-wrap: wrap; justify-content: center; }
.prompt-example {
    background-color: var(--sidebar-bg); border: 1px solid var(--border-color); padding: 10px 15px;
    border-radius: 8px; cursor: pointer; transition: all 0.2s ease; font-size: 14px;
}
.prompt-example:hover { border-color: var(--primary-color); background-color: #fff; }

/* Messages */
.message-wrapper { display: flex; gap: 15px; max-width: 90%; margin-bottom: 20px; }
.message-wrapper.user { align-self: flex-end; flex-direction: row-reverse; }
.message-wrapper.ai { align-self: flex-start; }

.avatar { width: 32px; height: 32px; border-radius: 50%; background-color: var(--ai-msg-bg); display: flex; align-items: center; justify-content: center; flex-shrink: 0; }
.avatar svg { width: 20px; height: 20px; color: var(--primary-color); }
.user .avatar { background-color: var(--user-msg-bg); color: white; }

.message { padding: 1px 18px; line-height: 1.7; word-wrap: break-word; }
.user-message { background-color: var(--user-msg-bg); color: white; border-radius: 18px 6px 18px 18px; padding: 12px 18px; }
.ai-message { background-color: var(--chat-bg); color: var(--text-color); border: 1px solid var(--border-color); border-radius: 6px 18px 18px 18px; }

.error-message { background-color: #fce8e6; color: #c5221f; align-self: center; text-align: center; font-size: 14px; font-weight: 500; padding: 12px 18px; border-radius: 12px; margin-bottom: 20px; }
.thinking { align-self: flex-start; color: var(--subtle-text-color); font-style: italic; margin-left: 47px; margin-bottom: 20px; }

/* Markdown Styles */
.ai-message pre { background-color: #202124; color: #e8eaed; padding: 16px; border-radius: 8px; overflow-x: auto; font-family: "Roboto Mono", monospace; }
.ai-message code { font-family: "Roboto Mono", monospace; background-color: rgba(0,0,0,0.08); padding: 2px 5px; border-radius: 4px; }
.ai-message p:first-child { margin-top: 12px; } .ai-message p:last-child { margin-bottom: 12px; }

/* Input Area */
#input-area { display: flex; padding: 15px; border-top: 1px solid var(--border-color); flex-shrink: 0; align-items: flex-end; gap: 10px; }
#input-wrapper { flex-grow: 1; position: relative; }
#user-input {
    width: 100%; padding: 12px 50px 12px 18px; box-sizing: border-box;
    border: 1px solid var(--border-color); border-radius: 22px; font-size: 16px; resize: none;
    max-height: 150px; overflow-y: auto; background-color: var(--sidebar-bg);
}
#user-input:focus { outline: none; border-color: var(--primary-color); background-color: #fff; }
#send-button {
    position: absolute; right: 6px; bottom: 6px;
    background-color: var(--primary-color); color: white; width: 32px; height: 32px; border: none; border-radius: 50%;
    cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.2s ease;
}
#send-button:disabled { background-color: #bdc1c6; cursor: not-allowed; }
#send-button svg { width: 18px; height: 18px; }
