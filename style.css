:root {
    --primary-color: #1a73e8;
    --sidebar-bg: #f0f4f9;
    --chat-bg: #ffffff;
    --text-color: #3c4043;
    --subtle-text-color: #5f6368;
    --user-msg-bg: #1a73e8;
    --ai-msg-bg: #f1f3f4;
    --border-color: #e0e2e6;
    --box-shadow: 0 1px 3px 0 rgba(0,0,0,.1), 0 1px 2px 0 rgba(0,0,0,.06);

    /* 响应式变量 */
    --sidebar-width: 260px;
    --mobile-header-height: 60px;
    --desktop-padding: 20px;
    --mobile-padding: 12px;
}

* {
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans SC', 'Google Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    margin: 0;
    background-color: #eaf0f6;
    color: var(--text-color);
    height: 100vh;
    overflow: hidden;
}

/* 桌面端布局 */
#app-container, #api-key-view {
    width: 100%;
    height: 100vh;
    background-color: var(--chat-bg);
    display: flex;
    overflow: hidden;
}

/* 桌面端容器样式 */
@media (min-width: 769px) {
    body {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: var(--desktop-padding);
    }

    #app-container, #api-key-view {
        max-width: 1200px;
        height: calc(100vh - 40px);
        max-height: 900px;
        border-radius: 16px;
        box-shadow: var(--box-shadow);
    }
}

/* API Key 输入界面 */
#api-key-view {
    padding: var(--mobile-padding);
    text-align: center;
    flex-direction: column;
    justify-content: center;
}

@media (min-width: 769px) {
    #api-key-view {
        padding: 40px;
    }
}

#api-key-view h2 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-weight: 500;
    font-size: 24px;
}

#api-key-view p {
    font-size: 14px;
    color: var(--subtle-text-color);
    margin-bottom: 25px;
    line-height: 1.5;
}

#api-key-input {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    font-size: 16px;
    margin-bottom: 20px;
}

#submit-api-key-button {
    background-color: var(--primary-color);
    color: white;
    padding: 12px 25px;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
    width: 100%;
    max-width: 200px;
}

/* --- Sidebar --- */
#sidebar {
    width: var(--sidebar-width);
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    padding: 12px;
    transition: transform 0.3s ease;
    z-index: 1000;
}

/* 移动端侧边栏 */
@media (max-width: 768px) {
    #sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        width: 280px;
        transform: translateX(-100%);
        box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    }

    #sidebar.mobile-open {
        transform: translateX(0);
    }

    /* 移动端遮罩层 */
    .mobile-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background-color: rgba(0,0,0,0.5);
        z-index: 999;
    }

    .mobile-overlay.active {
        display: block;
    }
}
#new-chat-button {
    display: flex; align-items: center; justify-content: center; gap: 8px;
    width: 100%; padding: 12px; background-color: #fff; color: var(--primary-color);
    border: 1px solid var(--primary-color); border-radius: 8px; font-size: 15px; font-weight: 500;
    cursor: pointer; margin-bottom: 15px; transition: all 0.2s ease;
}
#new-chat-button:hover { background-color: var(--ai-msg-bg); }
#new-chat-button svg { width: 18px; height: 18px; }

#chat-list { list-style: none; padding: 0; margin: 0; overflow-y: auto; flex-grow: 1; }
#chat-list::-webkit-scrollbar { width: 4px; }
#chat-list::-webkit-scrollbar-thumb { background: #ccc; border-radius: 2px; }

#chat-list .chat-item {
    display: flex; align-items: center; justify-content: space-between;
    padding: 8px 12px; border-radius: 8px; font-size: 14px; margin-bottom: 5px;
    transition: background-color 0.2s ease; position: relative;
}
#chat-list .chat-item:hover { background-color: #e8eaf6; }
#chat-list .chat-item.active-chat { background-color: #e8eaf6; color: var(--primary-color); font-weight: 500; }

.chat-title {
    flex-grow: 1; cursor: pointer; white-space: nowrap;
    overflow: hidden; text-overflow: ellipsis; padding-right: 8px;
}

.delete-chat-btn {
    background: none; border: none; cursor: pointer; color: var(--subtle-text-color);
    padding: 4px; border-radius: 4px; display: flex; align-items: center; justify-content: center;
    transition: all 0.2s ease; opacity: 0; flex-shrink: 0;
}
.chat-item:hover .delete-chat-btn { opacity: 1; }
.delete-chat-btn:hover { background-color: #f1f3f4; color: #d93025; }
.delete-chat-btn svg { width: 16px; height: 16px; }

/* 设置按钮 */
.settings-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    padding: 10px 12px;
    background-color: transparent;
    color: var(--subtle-text-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    margin-top: auto;
    margin-bottom: 0;
    transition: all 0.2s ease;
}

.settings-btn:hover {
    background-color: var(--ai-msg-bg);
    color: var(--text-color);
    border-color: var(--subtle-text-color);
}

.settings-btn svg {
    width: 16px;
    height: 16px;
}

@media (max-width: 768px) {
    .settings-btn span {
        display: none;
    }

    .settings-btn {
        justify-content: center;
        padding: 12px;
    }
}

/* --- Main Chat Area --- */
#chat-view-container {
    display: none;
    flex-direction: column;
    height: 100%;
    flex-grow: 1;
}

header {
    background-color: var(--chat-bg);
    color: var(--text-color);
    padding: 10px 20px;
    font-size: 16px;
    font-weight: 500;
    flex-shrink: 0;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

#chat-title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-grow: 1;
    text-align: center;
}

/* 移动端菜单按钮 */
#mobile-menu-button {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-color);
    padding: 8px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
    position: absolute;
    left: 15px;
}

#mobile-menu-button:hover {
    background-color: var(--ai-msg-bg);
}

#mobile-menu-button svg {
    width: 20px;
    height: 20px;
}

.header-spacer {
    width: 36px;
    position: absolute;
    right: 15px;
}

/* 响应式显示/隐藏 */
.mobile-only {
    display: none;
}

@media (max-width: 768px) {
    .mobile-only {
        display: flex;
    }

    header {
        justify-content: space-between;
        padding: 15px;
    }

    #chat-title {
        text-align: center;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        max-width: calc(100% - 100px);
    }
}

#chatbox {
    flex-grow: 1;
    padding: var(--desktop-padding);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

@media (max-width: 768px) {
    #chatbox {
        padding: var(--mobile-padding);
    }
}

/* Welcome Screen */
#welcome-screen {
    display: none; flex-direction: column; align-items: center; justify-content: center; height: 100%; text-align: center;
}
#welcome-screen .welcome-logo {
    width: 64px; height: 64px; background-color: var(--primary-color); color: white;
    border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-bottom: 20px;
}
#welcome-screen .welcome-logo svg { width: 36px; height: 36px; }
#welcome-screen h2 { margin: 0 0 10px 0; font-weight: 500; }
#welcome-screen p { color: var(--subtle-text-color); margin-top: 0; }
.prompt-examples {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.prompt-example {
    background-color: var(--sidebar-bg);
    border: 1px solid var(--border-color);
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    text-align: center;
}

.prompt-example:hover {
    border-color: var(--primary-color);
    background-color: #fff;
}

@media (max-width: 768px) {
    .prompt-examples {
        flex-direction: column;
        gap: 8px;
        margin-top: 15px;
    }

    .prompt-example {
        padding: 12px 16px;
        font-size: 15px;
        width: 100%;
        max-width: 300px;
    }

    #welcome-screen h2 {
        font-size: 20px;
        margin-bottom: 8px;
    }

    #welcome-screen p {
        font-size: 14px;
        margin-bottom: 15px;
    }

    #welcome-screen .welcome-logo {
        width: 56px;
        height: 56px;
        margin-bottom: 15px;
    }

    #welcome-screen .welcome-logo svg {
        width: 32px;
        height: 32px;
    }
}

/* Messages */
.message-wrapper {
    display: flex;
    gap: 15px;
    max-width: 90%;
    margin-bottom: 20px;
}
.message-wrapper.user {
    align-self: flex-end;
    flex-direction: row-reverse;
}
.message-wrapper.ai {
    align-self: flex-start;
}

@media (max-width: 768px) {
    .message-wrapper {
        max-width: 95%;
        gap: 10px;
        margin-bottom: 15px;
    }
}

.avatar { width: 32px; height: 32px; border-radius: 50%; background-color: var(--ai-msg-bg); display: flex; align-items: center; justify-content: center; flex-shrink: 0; }
.avatar svg { width: 20px; height: 20px; color: var(--primary-color); }
.user .avatar { background-color: var(--user-msg-bg); color: white; }

.message { padding: 1px 18px; line-height: 1.7; word-wrap: break-word; }
.user-message { background-color: var(--user-msg-bg); color: white; border-radius: 18px 6px 18px 18px; padding: 12px 18px; }
.ai-message { background-color: var(--chat-bg); color: var(--text-color); border: 1px solid var(--border-color); border-radius: 6px 18px 18px 18px; }

.error-message { background-color: #fce8e6; color: #c5221f; align-self: center; text-align: center; font-size: 14px; font-weight: 500; padding: 12px 18px; border-radius: 12px; margin-bottom: 20px; }
.thinking { align-self: flex-start; color: var(--subtle-text-color); font-style: italic; margin-left: 47px; margin-bottom: 20px; }

/* Markdown Styles */
.ai-message pre { background-color: #202124; color: #e8eaed; padding: 16px; border-radius: 8px; overflow-x: auto; font-family: "Roboto Mono", monospace; }
.ai-message code { font-family: "Roboto Mono", monospace; background-color: rgba(0,0,0,0.08); padding: 2px 5px; border-radius: 4px; }
.ai-message p:first-child { margin-top: 12px; } .ai-message p:last-child { margin-bottom: 12px; }

/* Input Area */
#input-area {
    display: flex;
    padding: 15px;
    border-top: 1px solid var(--border-color);
    flex-shrink: 0;
    align-items: flex-end;
    gap: 10px;
}

#input-wrapper {
    flex-grow: 1;
    position: relative;
}

#user-input {
    width: 100%;
    padding: 12px 50px 12px 18px;
    border: 1px solid var(--border-color);
    border-radius: 22px;
    font-size: 16px;
    resize: none;
    max-height: 150px;
    overflow-y: auto;
    background-color: var(--sidebar-bg);
    line-height: 1.4;
}

#user-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: #fff;
}

#send-button {
    position: absolute;
    right: 6px;
    bottom: 6px;
    background-color: var(--primary-color);
    color: white;
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

#send-button:disabled {
    background-color: #bdc1c6;
    cursor: not-allowed;
}

#send-button svg {
    width: 18px;
    height: 18px;
}

@media (max-width: 768px) {
    #input-area {
        padding: var(--mobile-padding);
    }

    #user-input {
        font-size: 16px; /* 防止iOS缩放 */
        padding: 14px 50px 14px 16px;
    }

    #send-button {
        width: 36px;
        height: 36px;
        right: 4px;
        bottom: 4px;
    }

    #send-button svg {
        width: 20px;
        height: 20px;
    }
}

/* 平板端优化 */
@media (min-width: 769px) and (max-width: 1024px) {
    #app-container, #api-key-view {
        max-width: 95vw;
        height: calc(100vh - 20px);
        margin: 10px;
    }

    #sidebar {
        width: 240px;
    }

    #chatbox {
        padding: 16px;
    }

    .message-wrapper {
        max-width: 92%;
    }
}

/* 大屏幕优化 */
@media (min-width: 1400px) {
    #app-container, #api-key-view {
        max-width: 1400px;
    }

    #sidebar {
        width: 300px;
    }

    #chatbox {
        padding: 30px;
    }

    .message-wrapper {
        max-width: 85%;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .delete-chat-btn {
        opacity: 1; /* 在触摸设备上始终显示删除按钮 */
    }

    .prompt-example {
        padding: 14px 18px;
        font-size: 16px;
    }

    #user-input {
        font-size: 16px; /* 防止iOS Safari缩放 */
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --subtle-text-color: #000000;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    #sidebar {
        transition: none;
    }
}

/* 模态框样式 */
.modal-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 2000;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(2px);
}

.modal-overlay.active {
    display: flex;
}

.modal {
    background-color: var(--chat-bg);
    border-radius: 16px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.close-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--subtle-text-color);
    padding: 8px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background-color: var(--ai-msg-bg);
    color: var(--text-color);
}

.close-btn svg {
    width: 20px;
    height: 20px;
}

.modal-content {
    padding: 24px;
}

.setting-group {
    margin-bottom: 20px;
}

.setting-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
    font-size: 14px;
}

.setting-group input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 14px;
    background-color: var(--sidebar-bg);
    color: var(--text-color);
    transition: all 0.2s ease;
}

.setting-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: var(--chat-bg);
    box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
}

.setting-hint {
    display: block;
    margin-top: 6px;
    font-size: 12px;
    color: var(--subtle-text-color);
    line-height: 1.4;
}

.modal-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
}

.primary-btn, .secondary-btn, .danger-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.primary-btn {
    background-color: var(--primary-color);
    color: white;
    flex: 1;
}

.primary-btn:hover {
    background-color: #1557b0;
}

.secondary-btn {
    background-color: var(--ai-msg-bg);
    color: var(--text-color);
    border: 1px solid var(--border-color);
    flex: 1;
}

.secondary-btn:hover {
    background-color: var(--sidebar-bg);
}

.danger-zone {
    border-top: 1px solid var(--border-color);
    padding-top: 20px;
}

.danger-zone h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: #d93025;
    font-weight: 600;
}

.danger-zone p {
    margin: 0 0 16px 0;
    font-size: 13px;
    color: var(--subtle-text-color);
    line-height: 1.5;
}

.danger-btn {
    background-color: #fce8e6;
    color: #d93025;
    border: 1px solid #f28b82;
    width: 100%;
    justify-content: center;
}

.danger-btn:hover {
    background-color: #d93025;
    color: white;
    border-color: #d93025;
}

.danger-btn svg {
    width: 16px;
    height: 16px;
}

@media (max-width: 768px) {
    .modal {
        width: 95%;
        margin: 20px;
        max-height: calc(100vh - 40px);
    }

    .modal-header {
        padding: 16px 20px 12px;
    }

    .modal-content {
        padding: 20px;
    }

    .modal-actions {
        flex-direction: column;
    }

    .primary-btn, .secondary-btn {
        flex: none;
    }
}
