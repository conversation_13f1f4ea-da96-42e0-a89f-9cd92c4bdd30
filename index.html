<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 对话应用</title>
    
    <!-- 引入 Google 字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    
    <!-- 引入样式文件 -->
    <link rel="stylesheet" href="style.css">
    
    <!-- 引入 Marked.js 库用于渲染 Markdown -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>
<body>

    <!-- 主应用容器，默认隐藏 -->
    <div id="app-container" style="display:none;">
        <!-- 移动端遮罩层 -->
        <div class="mobile-overlay" id="mobile-overlay"></div>

        <div id="sidebar">
            <button id="new-chat-button"></button>
            <ul id="chat-list"></ul>
        </div>
        <div id="chat-view-container">
            <header>
                <button id="mobile-menu-button" class="mobile-only">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                    </svg>
                </button>
                <span id="chat-title">对话</span>
                <div class="header-spacer mobile-only"></div>
            </header>
            <div id="chatbox">
                <!-- 新对话的欢迎界面 -->
                <div id="welcome-screen">
                    <div class="welcome-logo"></div>
                    <h2>你好，我是你的AI助手</h2>
                    <p>我可以回答问题、提供灵感、总结文本等等</p>
                    <div class="prompt-examples">
                        <div class="prompt-example">写一首关于宇宙的诗</div>
                        <div class="prompt-example">用简单的语言解释什么是黑洞</div>
                        <div class="prompt-example">给我推荐三部科幻电影</div>
                    </div>
                </div>
            </div>
            <div id="input-area">
                <div id="input-wrapper">
                    <textarea id="user-input" placeholder="输入消息..." rows="1"></textarea>
                    <button id="send-button" title="发送"></button>
                </div>
            </div>
        </div>
    </div>

    <!-- API Key 输入视图 -->
    <div id="api-key-view">
        <h2>欢迎使用 AI 对话应用</h2>
        <p>请输入您的 API Key 以开始。对话历史将自动保存在您的浏览器中。</p>
        <input type="password" id="api-key-input" placeholder="在此输入您的 API Key">
        <button id="submit-api-key-button">开始聊天</button>
    </div>

    <!-- 引入逻辑脚本文件，defer 属性确保在 HTML 解析完毕后执行 -->
    <script src="script.js" defer></script>
</body>
</html>
