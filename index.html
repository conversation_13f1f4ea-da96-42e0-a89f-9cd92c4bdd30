<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 对话应用</title>
    
    <!-- 引入 Google 字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    
    <!-- 引入样式文件 -->
    <link rel="stylesheet" href="style.css">
    
    <!-- 引入 Marked.js 库用于渲染 Markdown -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
</head>
<body>

    <!-- 主应用容器，默认隐藏 -->
    <div id="app-container" style="display:none;">
        <!-- 移动端遮罩层 -->
        <div class="mobile-overlay" id="mobile-overlay"></div>

        <div id="sidebar">
            <button id="new-chat-button"></button>
            <ul id="chat-list"></ul>
            <button id="settings-button" class="settings-btn" title="设置">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .***********.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/>
                </svg>
                <span>设置</span>
            </button>
        </div>
        <div id="chat-view-container">
            <header>
                <button id="mobile-menu-button" class="mobile-only">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                    </svg>
                </button>
                <span id="chat-title">对话</span>
                <div class="header-spacer mobile-only"></div>
            </header>
            <div id="chatbox">
                <!-- 新对话的欢迎界面 -->
                <div id="welcome-screen">
                    <div class="welcome-logo"></div>
                    <h2>你好，我是你的AI助手</h2>
                    <p>我可以回答问题、提供灵感、总结文本等等</p>
                    <div class="prompt-examples">
                        <div class="prompt-example">写一首关于宇宙的诗</div>
                        <div class="prompt-example">用简单的语言解释什么是黑洞</div>
                        <div class="prompt-example">给我推荐三部科幻电影</div>
                    </div>
                </div>
            </div>
            <div id="input-area">
                <div id="input-wrapper">
                    <textarea id="user-input" placeholder="输入消息..." rows="1"></textarea>
                    <button id="send-button" title="发送"></button>
                </div>
            </div>
        </div>
    </div>

    <!-- API Key 输入视图 -->
    <div id="api-key-view">
        <h2>欢迎使用 AI 对话应用</h2>
        <p>请输入您的 API Key 以开始。对话历史将自动保存在您的浏览器中。</p>
        <input type="password" id="api-key-input" placeholder="在此输入您的 API Key">
        <button id="submit-api-key-button">开始聊天</button>
    </div>

    <!-- 设置模态框 -->
    <div id="settings-overlay" class="modal-overlay">
        <div id="settings-modal" class="modal">
            <div class="modal-header">
                <h3>设置</h3>
                <button id="close-settings-button" class="close-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                    </svg>
                </button>
            </div>
            <div class="modal-content">
                <div class="setting-group">
                    <label for="settings-api-key-input">API Key</label>
                    <input type="password" id="settings-api-key-input" placeholder="输入您的 API Key">
                </div>
                <div class="setting-group">
                    <label for="settings-api-url-input">API 地址</label>
                    <input type="text" id="settings-api-url-input" placeholder="输入 API 地址">
                    <small class="setting-hint">
                        留空使用默认 Gemini API 地址<br>
                        <span class="default-api-url">默认地址：<code id="default-api-display"></code></span><br>
                        <span class="current-api-url">当前使用：<code id="current-api-display"></code></span>
                    </small>
                </div>
                <div class="modal-actions">
                    <button id="save-settings-button" class="primary-btn">保存设置</button>
                    <button id="reset-settings-button" class="secondary-btn">重置为默认</button>
                </div>
                <div class="danger-zone">
                    <h4>危险操作</h4>
                    <p>以下操作将永久删除所有本地数据，包括对话历史和设置，且无法恢复。</p>
                    <button id="clear-all-data-button" class="danger-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
                        </svg>
                        删除全部本地数据
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入逻辑脚本文件，defer 属性确保在 HTML 解析完毕后执行 -->
    <script src="script.js" defer></script>
</body>
</html>
