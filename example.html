<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Insight - 设置您的AI体验</title>
    <!-- 引入Google Fonts - Inter，用于提升文本显示效果 -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght=300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* CSS Reset and Global Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        :root {
            --primary-bg: #F0F2F5; /* 整体页面背景 */
            --card-bg: #FFFFFF; /* 卡片背景 */
            --brand-color: #3B82F6; /* 品牌强调色 - 蓝色 */
            --brand-color-light: #60A5FA; /* 品牌强调色 - 浅蓝色 */
            --text-color-dark: #333333; /* 深色文本 */
            --text-color-medium: #555555; /* 中等文本 */
            --text-color-light: #888888; /* 浅色文本 */
            --border-color: #E0E0E0; /* 边框颜色 */
            --shadow-light: rgba(0, 0, 0, 0.08); /* 轻微阴影 */
            --focus-ring: #bfdbfe; /* 焦点环颜色 */
            --warning-color: #EF4444; /* 警告颜色 */
            --success-color: #22C55E; /* 成功颜色 */
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', sans-serif;
            line-height: 1.6;
            color: var(--text-color-dark);
            background-color: var(--primary-bg);
            /* Centering content, taking full viewport height */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px; /* Padding for small screens to prevent content touching edges */
        }

        /* Main Container for the Setup Interface */
        .main-setup-container {
            background-color: var(--card-bg);
            border-radius: 16px;
            box-shadow: 0 10px 30px var(--shadow-light);
            padding: 30px; /* Default padding for smaller screens */
            width: 100%; /* Take full width on small screens */
            max-width: 700px; /* Max width for larger screens */
            text-align: center;
            display: flex;
            flex-direction: column;
            gap: 25px; /* Spacing between sections */
        }

        .main-setup-header {
            margin-bottom: 15px; /* Slightly less margin for small screens */
        }

        .main-setup-header .logo {
            font-size: 44px; /* Slightly smaller for mobile */
            color: var(--brand-color);
            margin-bottom: 12px;
            line-height: 1;
            display: block;
        }

        .main-setup-header h1 {
            font-size: 2em; /* Adjusted for mobile */
            font-weight: 700;
            color: var(--text-color-dark);
            margin-bottom: 8px;
        }

        .main-setup-header p {
            font-size: 1em; /* Adjusted for mobile */
            color: var(--text-color-medium);
            max-width: 500px;
            margin: 0 auto;
        }

        /* Common Section Styling */
        .setup-section {
            background-color: var(--primary-bg);
            border-radius: 12px;
            padding: 20px; /* Default padding for sections */
            text-align: left;
            border: 1px solid var(--border-color);
        }

        .setup-section h2 {
            font-size: 1.4em; /* Adjusted for mobile */
            font-weight: 600;
            color: var(--text-color-dark);
            margin-bottom: 18px;
            border-bottom: 1px dashed var(--border-color);
            padding-bottom: 12px;
        }

        /* Model Provider Selection - Grid for responsiveness */
        .radio-group {
            display: grid;
            grid-template-columns: 1fr; /* Default: Stack vertically on small screens */
            gap: 12px; /* Smaller gap for mobile */
        }

        .radio-group label {
            display: flex;
            align-items: center;
            padding: 10px 15px; /* Smaller padding for mobile */
            border: 1px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.95em; /* Smaller font size for mobile */
            color: var(--text-color-dark);
            transition: all 0.2s ease;
            user-select: none;
        }

        .radio-group label:hover {
            border-color: var(--brand-color-light);
            background-color: rgba(59, 130, 246, 0.05);
        }

        .radio-group input[type="radio"] {
            margin-right: 8px;
            transform: scale(1); /* Default scale */
            accent-color: var(--brand-color);
        }

        .radio-group input[type="radio"]:checked + span {
            font-weight: 600;
        }

        /* API Key Input Section */
        .api-key-input-group {
            position: relative;
            display: flex;
            align-items: center;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
            background-color: var(--card-bg);
            margin-top: 15px;
        }

        .api-key-input-group:focus-within {
            border-color: var(--brand-color-light);
            box-shadow: 0 0 0 4px var(--focus-ring);
        }

        .api-key-input-group input[type="password"],
        .api-key-input-group input[type="text"] {
            flex-grow: 1;
            padding: 12px 15px; /* Smaller padding for mobile */
            border: none;
            background: none;
            font-size: 0.95em; /* Smaller font for mobile */
            outline: none;
            color: var(--text-color-dark);
            border-radius: 10px;
        }

        .api-key-input-group .toggle-visibility-btn {
            background: none;
            border: none;
            padding: 0 12px; /* Smaller padding */
            cursor: pointer;
            color: var(--text-color-light);
            font-size: 18px; /* Smaller icon */
            transition: color 0.2s ease;
            flex-shrink: 0;
            line-height: 1;
        }
        .api-key-input-group .toggle-visibility-btn:hover {
            color: var(--text-color-dark);
        }
        .api-key-input-group .toggle-visibility-btn svg {
            width: 20px; /* Smaller SVG */
            height: 20px; /* Smaller SVG */
            fill: currentColor;
        }

        .api-key-info {
            display: flex;
            flex-direction: column; /* Stack vertically by default on small screens */
            align-items: flex-start; /* Align items to start */
            margin-top: 10px;
            gap: 8px; /* Smaller gap */
        }

        .api-key-link {
            font-size: 0.85em; /* Smaller font */
            color: var(--brand-color);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: color 0.2s ease;
            line-height: 1; /* Better alignment with icon */
        }
        .api-key-link:hover {
            color: var(--brand-color-light);
            text-decoration: underline;
        }

        .api-key-link svg {
            width: 14px; /* Smaller SVG */
            height: 14px; /* Smaller SVG */
            fill: currentColor;
        }

        .api-key-warning {
            color: var(--warning-color);
            font-size: 0.75em; /* Smaller font */
            flex-grow: 1;
            text-align: left; /* Align to left on small screens */
        }

        .control-buttons-group {
            display: flex;
            flex-direction: column; /* Stack buttons vertically on small screens */
            gap: 12px; /* Smaller gap */
            margin-top: 20px; /* Smaller margin-top */
        }

        .control-buttons-group button {
            width: 100%; /* Full width on small screens */
            padding: 12px 20px; /* Smaller padding for buttons */
            border: none;
            border-radius: 10px;
            font-size: 1em; /* Adjusted font size */
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s ease, transform 0.1s ease;
            box-shadow: 0 4px 10px rgba(0,0,0,0.08); /* Lighter shadow */
        }

        .test-connection-btn {
            background-color: #6B7280;
            color: white;
        }
        .test-connection-btn:hover {
            background-color: #4B5563;
        }
        .test-connection-btn:disabled {
            background-color: #D1D5DB;
            cursor: not-allowed;
            box-shadow: none;
        }

        .start-app-btn {
            background-color: var(--brand-color);
            color: white;
        }
        .start-app-btn:hover {
            background-color: var(--brand-color-light);
        }
        .start-app-btn:disabled {
            background-color: #A0C3F2;
            cursor: not-allowed;
            box-shadow: none;
        }

        /* Toast notifications (already adaptive but ensuring consistency) */
        .toast-notification {
            bottom: 20px; /* Adjust position for mobile views */
            left: 50%;
            transform: translateX(-50%);
            padding: 10px 18px; /* Smaller padding */
            font-size: 0.9em; /* Smaller font */
        }

        /* --- Media Queries for Larger Screens (Tablet and Desktop) --- */
        @media (min-width: 768px) {
            body {
                padding: 40px; /* More padding for larger screens */
            }

            .main-setup-container {
                padding: 40px; /* Larger padding for the main card */
                gap: 30px; /* More space between sections */
            }

            .main-setup-header .logo {
                font-size: 48px; /* Restore larger logo */
            }

            .main-setup-header h1 {
                font-size: 2.5em; /* Restore larger title */
            }

            .main-setup-header p {
                font-size: 1.1em; /* Restore larger text */
            }

            .setup-section {
                padding: 25px; /* Restore larger section padding */
            }

            .setup-section h2 {
                font-size: 1.6em; /* Restore larger section titles */
            }

            /* Model Provider Selection: 2 or 3 columns on larger screens */
            .radio-group {
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); /* Auto-fit columns */
                gap: 15px; /* Restore larger gap */
            }

            .radio-group label {
                padding: 12px 18px; /* Restore larger padding */
                font-size: 1.05em; /* Restore larger font */
            }

            .radio-group input[type="radio"] {
                 transform: scale(1.1); /* Restore larger radio button */
            }

            .api-key-input-group input[type="password"],
            .api-key-input-group input[type="text"] {
                padding: 15px 20px; /* Restore larger padding */
                font-size: 1em; /* Restore larger font */
            }

            .api-key-input-group .toggle-visibility-btn {
                padding: 0 15px; /* Restore larger padding */
                font-size: 20px; /* Restore larger icon */
            }
            .api-key-input-group .toggle-visibility-btn svg {
                width: 22px; /* Restore larger SVG */
                height: 22px; /* Restore larger SVG */
            }

            .api-key-info {
                flex-direction: row; /* Horizontal layout for info and warning */
                align-items: center; /* Align items to center */
                gap: 10px;
            }

            .api-key-link {
                font-size: 0.95em; /* Restore larger font */
            }
            .api-key-link svg {
                width: 16px; /* Restore larger SVG */
                height: 16px; /* Restore larger SVG */
            }

            .api-key-warning {
                font-size: 0.85em; /* Restore larger font */
                text-align: right; /* Align warning to right on larger screens */
            }

            .control-buttons-group {
                flex-direction: row; /* Horizontal layout for test and start buttons */
                gap: 15px; /* Restore larger gap */
                margin-top: 25px; /* Restore larger margin-top */
            }

            .control-buttons-group button {
                padding: 14px 25px; /* Restore larger padding */
                font-size: 1.1em; /* Restore larger font */
            }
        }
    </style>
</head>
<body>

    <div class="main-setup-container">
        <!-- 头部区域：应用名称与欢迎语 -->
        <div class="main-setup-header">
            <span class="logo">✨</span>
            <h1>欢迎使用 AI Insight</h1>
            <p>在这里，您可以配置您的语言模型服务提供商，轻松开启与AI的智能对话之旅。</p>
        </div>

        <!-- 模型供应商选择区 -->
        <div class="setup-section">
            <h2>选择您的模型供应商</h2>
            <div class="radio-group" role="radiogroup" aria-label="选择您的AI模型供应商">
                <label>
                    <input type="radio" name="model_provider" value="OpenAI" checked data-apikey-url="https://platform.openai.com/api-keys">
                    <span>OpenAI</span>
                </label>
                <label>
                    <input type="radio" name="model_provider" value="GoogleAI" data-apikey-url="https://ai.google.dev/gemini-api/explorer/api_key">
                    <span>Google AI</span>
                </label>
                <label>
                    <input type="radio" name="model_provider" value="Anthropic" data-apikey-url="https://console.anthropic.com/settings/teams">
                    <span>Anthropic</span>
                </label>
                <label>
                    <input type="radio" name="model_provider" value="Custom" data-apikey-url="#">
                    <span>自定义</span>
                </label>
            </div>
        </div>

        <!-- API Key 输入与管理区 -->
        <div class="setup-section">
            <h2>您的API Key</h2>
            <div class="api-key-input-group">
                <input type="password" id="api-key-input" placeholder="在此输入您的API Key" autocomplete="off" aria-label="API Key输入框">
                <button class="toggle-visibility-btn" id="toggle-api-key-visibility" aria-label="Toggle API Key visibility">
                    <!-- Eye open icon SVG -->
                    <svg id="eye-open-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="display: block;">
                        <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zm0 13c-3.03 0-5.5-2.47-5.5-5.5s2.47-5.5 5.5-5.5 5.5 2.47 5.5 5.5-2.47 5.5-5.5 5.5zm0-8.5c-1.93 0-3.5 1.57-3.5 3.5s1.57 3.5 3.5 3.5 3.5-1.57 3.5-3.5-1.57-3.5-3.5-3.5z" fill="currentColor"/>
                    </svg>
                    <!-- Eye closed icon SVG -->
                    <svg id="eye-closed-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="display: none;">
                        <path opacity="0.4" d="M12 7c2.76 0 5 2.24 5 5 0 .34-.04.67-.09 1H22c-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7L8.03 8.03A5.954 5.954 0 0 1 12 7z" fill="currentColor"/>
                        <path d="M2 4.27l2.28 2.28.46.46C3.06 7.91 1 12 1 12s4.27 7.5 11 7.5c1.09 0 2.16-.18 3.17-.5L17.73 20l1.27 1.27 1.4-1.4L3.4 2.87 2 4.27zm10 13c-3.03 0-5.5-2.47-5.5-5.5 0-.41.06-.82.16-1.2l1.76 1.76c-.03.18-.08.38-.08.64 0 1.93 1.57 3.5 3.5 3.5.26 0 .47-.05.64-.08l1.76 1.76c-.38.1-.79.16-1.2.16zm2.25-4.47l-2.02-2.02c.06-.04.12-.07.18-.09.64-.19 1.25-.09 1.72.38.47.47.56 1.08.38 1.72-.02.06-.05.12-.09.18z" fill="currentColor"/>
                    </svg>
                </button>
            </div>
            <div class="api-key-info">
                <a href="#" id="api-key-help-link" class="api-key-link" target="_blank" rel="noopener noreferrer">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M10 6H6C4.89543 6 4 6.89543 4 8V18C4 19.1046 4.89543 20 6 20H16C17.1046 20 18 19.1046 18 18V14"></path>
                        <path d="M14 4L20 4V10"></path>
                        <path d="M20 4L11 13"></path>
                    </svg>
                    如何获取OpenAI API Key？
                </a>
                <p class="api-key-warning" id="api-key-warning">请妥善保管您的API Key，切勿与他人分享！</p>
            </div>
            <div class="control-buttons-group">
                <button id="test-connection-btn" class="test-connection-btn">测试连接</button>
                <button id="start-app-btn" class="start-app-btn" disabled>保存并进入应用</button>
            </div>
        </div>
    </div>

    <!-- Toast Notification -->
    <div id="toast-notification" class="toast-notification" role="status" aria-live="polite"></div>

    <script>
        // DOM Elements
        const apiKeyInput = document.getElementById('api-key-input');
        const toggleApiKeyVisibilityBtn = document.getElementById('toggle-api-key-visibility');
        const eyeOpenIcon = document.getElementById('eye-open-icon');
        const eyeClosedIcon = document.getElementById('eye-closed-icon');
        const modelProviderRadios = document.querySelectorAll('input[name="model_provider"]');
        const apiKeyHelpLink = document.getElementById('api-key-help-link');
        const testConnectionBtn = document.getElementById('test-connection-btn');
        const startAppBtn = document.getElementById('start-app-btn');
        const toastNotification = document.getElementById('toast-notification');

        // --- Global State for API Keys (using LocalStorage for persistence) ---
        let apiKeys = JSON.parse(localStorage.getItem('aiInsightApiKeys')) || {
            OpenAI: '',
            GoogleAI: '',
            Anthropic: '',
            Custom: ''
        };
        let selectedProvider = document.querySelector('input[name="model_provider"]:checked').value;

        // --- Utility Functions ---
        function showToast(message, type = 'info', duration = 3000) {
            toastNotification.textContent = message;
            // Set ARIA live region attributes to announce changes for screen readers
            if (type === 'success') {
                toastNotification.setAttribute('aria-label', '成功通知');
                toastNotification.style.backgroundColor = 'var(--success-color)';
            } else if (type === 'error') {
                toastNotification.setAttribute('aria-label', '错误通知');
                toastNotification.style.backgroundColor = 'var(--warning-color)';
            } else {
                toastNotification.setAttribute('aria-label', '信息通知');
                toastNotification.style.backgroundColor = 'rgba(51, 51, 51, 0.9)';
            }

            toastNotification.classList.add('show');

            // Clear previous timeout to prevent issues if toasts queue up
            clearTimeout(toastNotification.dataset.timeoutId);

            const timeoutId = setTimeout(() => {
                toastNotification.classList.remove('show');
                // Reset background after animation completes if needed, or rely on CSS
            }, duration);
            toastNotification.dataset.timeoutId = timeoutId;
        }

        function updateStartAppButtonState() {
            startAppBtn.disabled = !apiKeys[selectedProvider] || apiKeys[selectedProvider].trim() === '';
        }

        function saveApiKey() {
            apiKeys[selectedProvider] = apiKeyInput.value.trim();
            localStorage.setItem('aiInsightApiKeys', JSON.stringify(apiKeys));
            updateStartAppButtonState();
        }

        // --- Core Logic ---

        // API Key Visibility Toggle
        toggleApiKeyVisibilityBtn.addEventListener('click', () => {
            if (apiKeyInput.type === 'password') {
                apiKeyInput.type = 'text';
                eyeOpenIcon.style.display = 'none';
                eyeClosedIcon.style.display = 'block';
                toggleApiKeyVisibilityBtn.setAttribute('aria-label', '隐藏 API Key');
            } else {
                apiKeyInput.type = 'password';
                eyeOpenIcon.style.display = 'block';
                eyeClosedIcon.style.display = 'none';
                toggleApiKeyVisibilityBtn.setAttribute('aria-label', '显示 API Key');
            }
        });

        // Model Provider Selection & API Key Link Update
        function updateApiKeySection() {
            selectedProvider = document.querySelector('input[name="model_provider"]:checked').value;
            const url = document.querySelector('input[name="model_provider"]:checked').dataset.apikeyUrl;

            // Update API Key Input Field
            apiKeyInput.value = apiKeys[selectedProvider] || '';
            apiKeyInput.placeholder = `在此输入您的${selectedToDisplay(selectedProvider)} API Key`;

            // Update API Key Help Link
            apiKeyHelpLink.href = url || '#';
            apiKeyHelpLink.textContent = `如何获取${selectedToDisplay(selectedProvider)} API Key？`;

            updateStartAppButtonState(); // Update button state on provider change
        }

        function selectedToDisplay(provider) {
             switch(provider) {
                case 'OpenAI': return 'OpenAI';
                case 'GoogleAI': return 'Google AI';
                case 'Anthropic': return 'Anthropic';
                case 'Custom': return '自定义';
                default: return provider;
             }
        }

        modelProviderRadios.forEach(radio => {
            radio.addEventListener('change', updateApiKeySection);
            // Set initial selected state to aria-checked
            if (radio.checked) {
                radio.setAttribute('aria-checked', 'true');
            } else {
                radio.setAttribute('aria-checked', 'false');
            }
            radio.addEventListener('click', () => {
                modelProviderRadios.forEach(r => r.setAttribute('aria-checked', 'false'));
                radio.setAttribute('aria-checked', 'true');
            });
        });

        // Initialize API key section on page load
        updateApiKeySection();

        // Save API Key on input change (saves to current session state, not localStorage immediately)
        // localStorage save happens on test/start button clicks
        apiKeyInput.addEventListener('input', () => {
             apiKeys[selectedProvider] = apiKeyInput.value.trim();
             updateStartAppButtonState(); // Enable/disable Start button as input changes
             // Also update localStorage on input, for more granular saving
             localStorage.setItem('aiInsightApiKeys', JSON.stringify(apiKeys));
        });

        // Test Connection Button (Dummy)
        testConnectionBtn.addEventListener('click', () => {
            const apiKey = apiKeyInput.value.trim();
            if (apiKey) {
                showToast(`正在测试 ${selectedToDisplay(selectedProvider)} 连接...`);
                // Simulate network request
                setTimeout(() => {
                    const isSuccess = Math.random() > 0.2; // Simulate 80% success rate
                    if (isSuccess) {
                        saveApiKey(); // Save API Key to localStorage on successful test
                        showToast(`🚀 ${selectedToDisplay(selectedProvider)} 连接成功！API Key 已保存。`, 'success');
                    } else {
                        showToast(`❌ ${selectedToDisplay(selectedProvider)} 连接失败，请检查 API Key 或网络。`, 'error');
                    }
                }, 1500);
            } else {
                showToast('请输入API Key后再测试。', 'info', 2000);
            }
        });

        // Start Application Button
        startAppBtn.addEventListener('click', () => {
            if (startAppBtn.disabled) {
                showToast('请先输入有效的API Key并保存。', 'info', 2000);
                return; // Should not happen if button is disabled properly
            }

            saveApiKey(); // Ensure the latest API key is saved before proceeding
            showToast('API Key 已保存，正在进入应用...', 'success');

            // Simulate navigation to the main chat interface
            // In a real application, this would be a redirect or a SPA view change
            setTimeout(() => {
                // In a real application, this would transition to the main chat UI.
                // For this HTML file, we'll show an alert or log to console.
                alert(`欢迎！您已成功配置 ${selectedToDisplay(selectedProvider)}。现在可以开始与AI交流了！`);
                // Example for a real app: window.location.href = '/chat-interface.html';
                console.log("Navigating to the main chat interface.");

            }, 1000);
        });

        // Initial check for button state on load
        updateStartAppButtonState();
    </script>
</body>
</html>
