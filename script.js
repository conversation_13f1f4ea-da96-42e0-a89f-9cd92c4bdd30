// --- DOM Elements ---
let elements = {};

// --- App State & Constants ---
let apiKey = '';
let apiUrl = '';
let allChats = [];
let currentChatId = null;
const MODEL_NAME = 'gemini-2.5-flash';
const DEFAULT_API_URL = `https://generativelanguage.googleapis.com/v1beta/models/${MODEL_NAME}:generateContent`;
const STORAGE_KEY = 'ai_multi_chat_pro_data';
const API_KEY_STORAGE_KEY = 'ai_multi_chat_pro_api_key';
const API_URL_STORAGE_KEY = 'ai_multi_chat_pro_api_url';

const ICONS = {
    newChat: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 4a1 1 0 0 1 1 1v6h6a1 1 0 1 1 0 2h-6v6a1 1 0 1 1-2 0v-6H5a1 1 0 1 1 0-2h6V5a1 1 0 0 1 1-1z"/></svg><span>新建对话</span>`,
    delete: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M17 6h5v2h-2v13a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V8H2V6h5V3a1 1 0 0 1 1-1h6a1 1 0 0 1 1 1v3zm-2-2H9v2h6V4z"/></svg>`,
    send: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M3.478 2.405a.75.75 0 00-.926.94l2.432 7.905H13.5a.75.75 0 010 1.5H4.984l-2.432 7.905a.75.75 0 00.926.94 60.519 60.519 0 0018.445-8.986.75.75 0 000-1.218A60.517 60.517 0 003.478 2.405z"/></svg>`,
    aiAvatar: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2a10 10 0 0 0-9.995 9.05L2 12a10 10 0 0 0 10 10c.88 0 1.73-.113 2.54-.325.298-.075.565.125.64.423.076.298-.125.565-.423.64A12 12 0 0 1 12 22 12 12 0 0 1 0 12 12 12 0 0 1 12 0a12 12 0 0 1 12 12v.05a1 1 0 0 1-1 1 1 1 0 0 0-1 1v1.95A10 10 0 0 0 12 2zm2 13h-4v2h4v-2zm-4-3h4v-2h-4v2z"/></svg>`,
    welcomeLogo: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12.0006 22.0001C6.47775 22.0001 2.00061 17.5229 2.00061 12.0001C2.00061 6.47721 6.47775 2.00008 12.0006 2.00008C17.5234 2.00008 22.0006 6.47721 22.0006 12.0001C22.0006 17.5229 17.5234 22.0001 12.0006 22.0001ZM11.0006 14.0001H13.0006V16.0001H11.0006V14.0001ZM11.0006 8.00008H13.0006V12.0001H11.0006V8.00008Z"></path></svg>`,
    settings: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/></svg>`,
    close: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>`,
    warning: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/></svg>`
};

// --- Event Listeners ---
document.addEventListener('DOMContentLoaded', () => {
    // 获取DOM元素
    elements = {
        appContainer: document.getElementById('app-container'),
        apiKeyView: document.getElementById('api-key-view'),
        apiKeyInput: document.getElementById('api-key-input'),
        submitApiKeyButton: document.getElementById('submit-api-key-button'),
        chatViewContainer: document.getElementById('chat-view-container'),
        newChatButton: document.getElementById('new-chat-button'),
        chatList: document.getElementById('chat-list'),
        chatTitle: document.getElementById('chat-title'),
        chatbox: document.getElementById('chatbox'),
        welcomeScreen: document.getElementById('welcome-screen'),
        userInput: document.getElementById('user-input'),
        sendButton: document.getElementById('send-button'),
        sidebar: document.getElementById('sidebar'),
        mobileMenuButton: document.getElementById('mobile-menu-button'),
        mobileOverlay: document.getElementById('mobile-overlay'),
        settingsButton: document.getElementById('settings-button'),
        settingsModal: document.getElementById('settings-modal'),
        settingsOverlay: document.getElementById('settings-overlay'),
        closeSettingsButton: document.getElementById('close-settings-button'),
        settingsApiKeyInput: document.getElementById('settings-api-key-input'),
        settingsApiUrlInput: document.getElementById('settings-api-url-input'),
        saveSettingsButton: document.getElementById('save-settings-button'),
        resetSettingsButton: document.getElementById('reset-settings-button'),
        clearAllDataButton: document.getElementById('clear-all-data-button')
    };

    // 设置图标
    elements.newChatButton.innerHTML = ICONS.newChat;
    elements.sendButton.innerHTML = ICONS.send;
    document.querySelector('.welcome-logo').innerHTML = ICONS.welcomeLogo;

    // 自动填充已保存的API key和URL
    loadApiKeyFromStorage();
    loadApiUrlFromStorage();

    // 设置事件监听器
    setupEventListeners();
});
function setupEventListeners() {
    // 基本事件监听器
    elements.submitApiKeyButton.addEventListener('click', initializeApp);
    elements.apiKeyInput.addEventListener('keydown', (e) => { if (e.key === 'Enter') initializeApp(); });
    elements.newChatButton.addEventListener('click', createNewChat);
    elements.sendButton.addEventListener('click', sendMessage);
    elements.userInput.addEventListener('keydown', (e) => { if (e.key === 'Enter' && !e.shiftKey) { e.preventDefault(); sendMessage(); } });
    elements.userInput.addEventListener('input', () => { elements.userInput.style.height = 'auto'; elements.userInput.style.height = (elements.userInput.scrollHeight) + 'px'; });

    // 移动端菜单事件监听
    elements.mobileMenuButton.addEventListener('click', toggleMobileSidebar);
    elements.mobileOverlay.addEventListener('click', closeMobileSidebar);

    // 设置相关事件监听
    elements.settingsButton.addEventListener('click', openSettings);
    elements.closeSettingsButton.addEventListener('click', closeSettings);
    elements.settingsOverlay.addEventListener('click', (e) => {
        if (e.target === elements.settingsOverlay) closeSettings();
    });
    elements.saveSettingsButton.addEventListener('click', saveSettings);
    elements.resetSettingsButton.addEventListener('click', resetSettings);
    elements.clearAllDataButton.addEventListener('click', clearAllData);

    // 设置输入框Enter键保存
    elements.settingsApiKeyInput.addEventListener('keydown', (e) => { if (e.key === 'Enter') saveSettings(); });
    elements.settingsApiUrlInput.addEventListener('keydown', (e) => { if (e.key === 'Enter') saveSettings(); });

    // 键盘快捷键
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            if (elements.settingsOverlay.classList.contains('active')) {
                closeSettings();
            }
        }
    });
}

document.querySelectorAll('.prompt-example').forEach(p => {
    p.addEventListener('click', () => {
        elements.userInput.value = p.textContent;
        sendMessage();
    });
});

// --- Initialization ---
function initializeApp() {
    const key = elements.apiKeyInput.value.trim();
    if (key) {
        apiKey = key;
        saveApiKeyToStorage(key); // 保存API key到本地存储
        elements.apiKeyView.style.display = 'none';
        elements.appContainer.style.display = 'flex';
        loadDataFromStorage();
        renderSidebar();
        renderChatContent();
        elements.userInput.focus();
    } else { alert('请输入有效的 API Key！'); }
}

// --- API Key & URL Storage Functions ---
function saveApiKeyToStorage(key) {
    try {
        localStorage.setItem(API_KEY_STORAGE_KEY, key);
    } catch (e) {
        console.error("保存API Key失败:", e);
    }
}

function loadApiKeyFromStorage() {
    try {
        const savedApiKey = localStorage.getItem(API_KEY_STORAGE_KEY);
        if (savedApiKey) {
            elements.apiKeyInput.value = savedApiKey;
            apiKey = savedApiKey;
        }
    } catch (e) {
        console.error("加载API Key失败:", e);
    }
}

function saveApiUrlToStorage(url) {
    try {
        localStorage.setItem(API_URL_STORAGE_KEY, url);
    } catch (e) {
        console.error("保存API URL失败:", e);
    }
}

function loadApiUrlFromStorage() {
    try {
        const savedApiUrl = localStorage.getItem(API_URL_STORAGE_KEY);
        apiUrl = savedApiUrl || DEFAULT_API_URL;
        return apiUrl;
    } catch (e) {
        console.error("加载API URL失败:", e);
        apiUrl = DEFAULT_API_URL;
        return apiUrl;
    }
}

function getApiUrl() {
    const baseUrl = apiUrl || DEFAULT_API_URL;
    return `${baseUrl}?key=${apiKey}`;
}

// --- Data Management ---
function loadDataFromStorage() {
    try {
        const data = JSON.parse(localStorage.getItem(STORAGE_KEY));
        if (data && data.chats && data.chats.length > 0 && data.currentChatId) {
            allChats = data.chats;
            currentChatId = data.currentChatId;
            if (!allChats.find(c => c.id === currentChatId)) {
                currentChatId = allChats[0]?.id || null;
            }
        }
    } catch (e) { console.error("解析本地数据失败:", e); }

    if (!allChats.length || !currentChatId) { createNewChat(false); }
}

function saveDataToStorage() { localStorage.setItem(STORAGE_KEY, JSON.stringify({ chats: allChats, currentChatId: currentChatId })); }

function createNewChat(switchImmediately = true) {
    const newId = Date.now().toString();
    const newChat = { id: newId, title: "新对话", history: [] };
    allChats.unshift(newChat);
    if (switchImmediately) { switchChat(newId); } 
    else { currentChatId = newId; }
    saveDataToStorage();
    renderSidebar();
}

function deleteChat(chatId) {
    if (allChats.length <= 1) {
        alert("这是最后一个对话，无法删除。");
        return;
    }

    const chatToDelete = allChats.find(c => c.id === chatId);
    if (!chatToDelete) return;

    if (confirm(`确定要删除对话 "${chatToDelete.title}" 吗？此操作无法撤销。`)) {
        const chatIndex = allChats.findIndex(c => c.id === chatId);
        allChats.splice(chatIndex, 1);

        // 如果删除的是当前对话，需要切换到其他对话
        if (chatId === currentChatId) {
            const newIndexToActivate = Math.max(0, chatIndex - 1);
            const newChatIdToActivate = allChats[newIndexToActivate].id;
            switchChat(newChatIdToActivate);
        } else {
            // 如果删除的不是当前对话，只需要重新渲染侧边栏和保存数据
            renderSidebar();
            saveDataToStorage();
        }
    }
}

// --- UI Rendering ---
function renderSidebar() {
    elements.chatList.innerHTML = '';
    allChats.forEach(chat => {
        const li = document.createElement('li');
        li.className = 'chat-item';
        li.dataset.chatId = chat.id;
        if (chat.id === currentChatId) li.classList.add('active-chat');

        // 创建对话标题容器
        const titleSpan = document.createElement('span');
        titleSpan.className = 'chat-title';
        titleSpan.textContent = chat.title;
        titleSpan.addEventListener('click', () => switchChat(chat.id));

        // 创建删除按钮
        const deleteButton = document.createElement('button');
        deleteButton.className = 'delete-chat-btn';
        deleteButton.innerHTML = ICONS.delete;
        deleteButton.title = '删除对话';
        deleteButton.addEventListener('click', (e) => {
            e.stopPropagation(); // 防止触发切换对话
            deleteChat(chat.id);
        });

        li.appendChild(titleSpan);
        li.appendChild(deleteButton);
        elements.chatList.appendChild(li);
    });
}

function renderChatContent() {
    const chat = getCurrentChat();
    if (!chat) return;

    elements.chatViewContainer.style.display = 'flex';
    elements.chatTitle.textContent = chat.title;
    elements.chatbox.innerHTML = ''; // Clear previous content

    if (chat.history.length === 0) {
        elements.chatbox.appendChild(elements.welcomeScreen);
        elements.welcomeScreen.style.display = 'flex';
    } else {
        elements.welcomeScreen.style.display = 'none';
        chat.history.forEach(msg => addMessageToChatbox(msg.role === 'model' ? 'ai' : 'user', msg.parts[0].text));
    }
}

function switchChat(chatId) {
    if (chatId === currentChatId) return;
    currentChatId = chatId;
    renderChatContent();
    renderSidebar();
    saveDataToStorage();

    // 移动端切换对话后自动关闭侧边栏
    if (window.innerWidth <= 768) {
        closeMobileSidebar();
    }
}

function addMessageToChatbox(sender, text) {
    elements.welcomeScreen.style.display = 'none';
    const wrapper = document.createElement('div');
    wrapper.className = `message-wrapper ${sender}`;
    
    const avatar = document.createElement('div');
    avatar.className = 'avatar';
    if (sender === 'ai') avatar.innerHTML = ICONS.aiAvatar;
    else avatar.innerHTML = 'U';

    const messageElement = document.createElement('div');
    messageElement.classList.add('message', `${sender}-message`);
    messageElement.innerHTML = sender === 'ai' ? marked.parse(text) : text.replace(/</g, "&lt;").replace(/>/g, "&gt;");
    
    wrapper.appendChild(avatar);
    wrapper.appendChild(messageElement);
    elements.chatbox.appendChild(wrapper);
    elements.chatbox.scrollTop = elements.chatbox.scrollHeight;
}

// --- Core Chat Logic ---
async function sendMessage() {
    const userText = elements.userInput.value.trim();
    if (!userText || elements.sendButton.disabled) return;
    
    const currentChat = getCurrentChat();
    if (!currentChat) return;

    if (currentChat.history.length === 0) {
        currentChat.title = userText.substring(0, 20) + (userText.length > 20 ? '...' : '');
        renderSidebar();
        elements.chatTitle.textContent = currentChat.title;
    }

    addMessageToChatbox('user', userText);
    elements.userInput.value = '';
    elements.userInput.style.height = 'auto';

    currentChat.history.push({ role: 'user', parts: [{ text: userText }] });
    saveDataToStorage();

    toggleInput(true);
    const thinkingElement = showThinkingIndicator();

    try {
        const response = await fetch(getApiUrl(), {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ contents: currentChat.history }),
        });

        thinkingElement.remove();
        const data = await response.json();

        if (response.ok && data.candidates && data.candidates.length > 0 && data.candidates[0].content) {
            const aiText = data.candidates[0].content.parts[0].text;
            currentChat.history.push({ role: 'model', parts: [{ text: aiText }] });
            addMessageToChatbox('ai', aiText);
        } else {
            const errorMsg = data.error?.message || '模型未返回有效回复。';
            throw new Error(errorMsg);
        }
    } catch (error) {
        console.error('Error calling API:', error);
        thinkingElement?.remove();
        addMessageToChatbox('error', `请求失败: ${error.message}`);
        currentChat.history.pop();
    } finally {
        saveDataToStorage();
        toggleInput(false);
    }
}

// --- Mobile Menu Functions ---
function toggleMobileSidebar() {
    const isOpen = elements.sidebar.classList.contains('mobile-open');
    if (isOpen) {
        closeMobileSidebar();
    } else {
        openMobileSidebar();
    }
}

function openMobileSidebar() {
    elements.sidebar.classList.add('mobile-open');
    elements.mobileOverlay.classList.add('active');
    document.body.style.overflow = 'hidden'; // 防止背景滚动
}

function closeMobileSidebar() {
    elements.sidebar.classList.remove('mobile-open');
    elements.mobileOverlay.classList.remove('active');
    document.body.style.overflow = ''; // 恢复滚动
}

// 检测屏幕尺寸变化，自动关闭移动端菜单
function handleResize() {
    if (window.innerWidth > 768) {
        closeMobileSidebar();
    }
}

window.addEventListener('resize', handleResize);

// --- Settings Functions ---
function openSettings() {
    // 填充当前设置值
    elements.settingsApiKeyInput.value = apiKey || '';
    elements.settingsApiUrlInput.value = (apiUrl === DEFAULT_API_URL) ? '' : apiUrl;

    // 显示默认API地址
    const defaultApiDisplay = document.getElementById('default-api-display');
    if (defaultApiDisplay) {
        defaultApiDisplay.textContent = DEFAULT_API_URL;
    }

    // 显示当前使用的API地址
    const currentApiDisplay = document.getElementById('current-api-display');
    if (currentApiDisplay) {
        currentApiDisplay.textContent = apiUrl || DEFAULT_API_URL;
    }

    elements.settingsOverlay.classList.add('active');
    document.body.style.overflow = 'hidden';
}

function closeSettings() {
    elements.settingsOverlay.classList.remove('active');
    document.body.style.overflow = '';
}

function saveSettings() {
    const newApiKey = elements.settingsApiKeyInput.value.trim();
    const newApiUrl = elements.settingsApiUrlInput.value.trim();

    if (!newApiKey) {
        alert('请输入有效的 API Key！');
        return;
    }

    // 保存API Key
    apiKey = newApiKey;
    saveApiKeyToStorage(newApiKey);

    // 保存API URL
    apiUrl = newApiUrl || DEFAULT_API_URL;
    saveApiUrlToStorage(apiUrl);

    // 更新初始页面的API Key输入框
    elements.apiKeyInput.value = newApiKey;

    // 更新当前API地址显示
    const currentApiDisplay = document.getElementById('current-api-display');
    if (currentApiDisplay) {
        currentApiDisplay.textContent = apiUrl;
    }

    closeSettings();
    alert('设置已保存！');
}

function resetSettings() {
    if (confirm('确定要重置所有设置为默认值吗？')) {
        // 重置为默认值
        apiUrl = DEFAULT_API_URL;

        // 清除存储的API URL
        try {
            localStorage.removeItem(API_URL_STORAGE_KEY);
        } catch (e) {
            console.error("清除API URL失败:", e);
        }

        // 更新输入框
        elements.settingsApiUrlInput.value = '';

        alert('设置已重置为默认值！');
    }
}

function clearAllData() {
    const confirmText = '删除全部数据';
    const userInput = prompt(
        `此操作将永久删除所有本地数据，包括：\n• 所有对话历史\n• API Key 和设置\n• 其他应用数据\n\n此操作无法撤销！\n\n如果确定要继续，请在下方输入"${confirmText}"：`
    );

    if (userInput === confirmText) {
        try {
            // 清除所有本地存储数据
            localStorage.removeItem(STORAGE_KEY);
            localStorage.removeItem(API_KEY_STORAGE_KEY);
            localStorage.removeItem(API_URL_STORAGE_KEY);

            // 重置应用状态
            apiKey = '';
            apiUrl = DEFAULT_API_URL;
            allChats = [];
            currentChatId = null;

            // 关闭设置模态框
            closeSettings();

            // 返回到API Key输入页面
            elements.appContainer.style.display = 'none';
            elements.apiKeyView.style.display = 'flex';
            elements.apiKeyInput.value = '';

            alert('所有本地数据已清除！');
        } catch (e) {
            console.error("清除数据失败:", e);
            alert('清除数据时发生错误，请刷新页面后重试。');
        }
    } else if (userInput !== null) {
        alert('输入不匹配，操作已取消。');
    }
}

// --- Helpers ---
function getCurrentChat() { return allChats.find(c => c.id === currentChatId); }
function showThinkingIndicator() {
    const el = document.createElement('div');
    el.classList.add('thinking');
    el.textContent = 'AI 正在思考中...';
    elements.chatbox.appendChild(el);
    elements.chatbox.scrollTop = elements.chatbox.scrollHeight;
    return el;
}
function toggleInput(disabled) {
    elements.userInput.disabled = disabled;
    elements.sendButton.disabled = disabled;
    if (!disabled) elements.userInput.focus();
}
